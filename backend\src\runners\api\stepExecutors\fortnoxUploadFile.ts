import axios from 'axios';
import FormData from 'form-data';
import type { FortnoxUploadFileStep } from '@rpa-project/shared/dist/esm/types/steps/api';
import { getDefaultVariableName } from '@rpa-project/shared';
import { StepExecutionResult } from '../../base';
import { customerService } from '../../../services/customerService';

/**
 * Executor context for Fortnox steps
 */
export interface FortnoxExecutorContext {
  variables: Record<string, any>;
  onLog: (log: { level: 'info' | 'warn' | 'error'; message: string; stepId?: string }) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
  customerId?: string;
}

/**
 * Fortnox Archive API response interface
 * Note: The actual response structure may vary, so we need to handle multiple formats
 */
interface FortnoxArchiveResponse {
  Archive?: {
    Id?: string;
    Name?: string;
    Size?: number;
    Path?: string;
  };
  // Alternative response format (if API returns different structure)
  Id?: string;
  Name?: string;
  Size?: number;
  Path?: string;
  // Allow any additional properties for flexible response handling
  [key: string]: any;
}

/**
 * Execute Fortnox Upload File step
 */
export async function executeFortnoxUploadFile(
  step: FortnoxUploadFileStep,
  context: FortnoxExecutorContext,
  stepIndex?: number
): Promise<StepExecutionResult> {
  const { variables, onLog, interpolateVariables, customerId } = context;

  try {
    onLog({
      level: 'info',
      message: `Executing Fortnox Upload File: ${step.id}`,
      stepId: step.id
    });

    // Get Fortnox token for the customer
    if (!customerId) {
      throw new Error('Customer ID is required for Fortnox API calls');
    }

    const fortnoxTokens = await customerService.getCustomerTokensWithData(customerId);
    const fortnoxToken = fortnoxTokens.find(token => token.provider === 'Fortnox' && token.apiToken);

    if (!fortnoxToken || !fortnoxToken.apiToken) {
      throw new Error('No valid Fortnox token found for customer');
    }

    // Get base64 file content from variable
    // Handle both direct variable names and ${variableName} syntax
    let actualVariableName = step.inputVariable;

    // Check if inputVariable contains ${...} syntax and extract the variable name
    const variableMatch = step.inputVariable.match(/^\$\{([^}]+)\}$/);
    if (variableMatch) {
      actualVariableName = variableMatch[1];
    }

    const base64Content = variables[actualVariableName];

    if (!base64Content) {
      onLog({
        level: 'error',
        message: `Available variables: ${Object.keys(variables).join(', ')}`,
        stepId: step.id
      });
      throw new Error(`No file content found in variable: ${actualVariableName}`);
    }

    // Extract base64 data (remove data URL prefix if present)
    let base64Data = base64Content;
    if (typeof base64Data === 'string' && base64Data.includes(',')) {
      base64Data = base64Data.split(',')[1];
    }

    // Convert base64 to buffer
    let fileBuffer: Buffer;
    try {
      fileBuffer = Buffer.from(base64Data, 'base64');
    } catch (bufferError) {
      throw new Error(`Invalid base64 data in variable ${actualVariableName}: ${bufferError instanceof Error ? bufferError.message : 'Unknown error'}`);
    }

    onLog({
      level: 'info',
      message: `File buffer created, size: ${fileBuffer.length} bytes`,
      stepId: step.id
    });

    // Determine filename
    let filename: string = step.filename || '';
    if (!filename) {
      // Try to get filename from related variable
      const filenameVariable = `${actualVariableName}_filename`;
      filename = variables[filenameVariable] || 'uploaded_file.bin';
    }
    filename = interpolateVariables(filename, variables);

    onLog({
      level: 'info',
      message: `Uploading file: ${filename}`,
      stepId: step.id
    });

    // Create FormData for multipart upload
    const formData = new FormData();
    formData.append('file', fileBuffer, {
      filename: filename,
      contentType: 'application/octet-stream'
    });

    // Add description if provided
    if (step.description) {
      const interpolatedDescription = interpolateVariables(step.description, variables);
      formData.append('description', interpolatedDescription);
    }

    // Upload file to Fortnox Archive
    let uploadResponse;
    try {
      uploadResponse = await axios.post(
        'https://api.fortnox.se/3/archive',
        formData,
        {
          headers: {
            'Authorization': `Bearer ${fortnoxToken.apiToken}`,
            'Accept': 'application/json',
            ...formData.getHeaders()
          },
          maxContentLength: Infinity,
          maxBodyLength: Infinity
        }
      );
    } catch (apiError: any) {
      // Log detailed error information from Fortnox API
      const errorDetails = apiError.response?.data || apiError.message;
      const statusCode = apiError.response?.status;

      onLog({
        level: 'error',
        message: `Fortnox Archive API error (${statusCode}): ${JSON.stringify(errorDetails, null, 2)}`,
        stepId: step.id
      });

      throw new Error(`Fortnox Archive API error (${statusCode}): ${JSON.stringify(errorDetails)}`);
    }

    // Log the actual response structure for debugging
    onLog({
      level: 'info',
      message: `Fortnox upload response: ${JSON.stringify(uploadResponse.data, null, 2)}`,
      stepId: step.id
    });

    const uploadedFile = uploadResponse.data as FortnoxArchiveResponse;

    // Extract file information from response (handle multiple possible structures)
    let fileId: string | undefined;
    let fileName: string;
    let fileSize: number;
    let filePath: string;
    let archiveData: any;

    if (uploadedFile.Archive && uploadedFile.Archive.Id) {
      // Standard expected format
      fileId = uploadedFile.Archive.Id;
      fileName = uploadedFile.Archive.Name || 'unknown';
      fileSize = uploadedFile.Archive.Size || 0;
      filePath = uploadedFile.Archive.Path || '';
      archiveData = uploadedFile.Archive;
    } else if (uploadedFile.Id) {
      // Alternative format (direct properties)
      fileId = uploadedFile.Id;
      fileName = uploadedFile.Name || 'unknown';
      fileSize = uploadedFile.Size || 0;
      filePath = uploadedFile.Path || '';
      archiveData = uploadedFile;
    } else {
      // Try to find any ID-like property in the response
      const responseKeys = Object.keys(uploadedFile);
      const idKey = responseKeys.find(key =>
        key.toLowerCase().includes('id') ||
        key.toLowerCase().includes('fileid') ||
        key.toLowerCase().includes('archiveid')
      );

      if (idKey && uploadedFile[idKey]) {
        onLog({
          level: 'warn',
          message: `Using alternative response structure. Found ID in property: ${idKey}`,
          stepId: step.id
        });

        fileId = uploadedFile[idKey];
        fileName = uploadedFile.Name || uploadedFile.name || uploadedFile.FileName || uploadedFile.filename || 'unknown';
        fileSize = uploadedFile.Size || uploadedFile.size || uploadedFile.FileSize || uploadedFile.filesize || 0;
        filePath = uploadedFile.Path || uploadedFile.path || uploadedFile.FilePath || uploadedFile.filepath || '';
        archiveData = uploadedFile;
      } else {
        onLog({
          level: 'error',
          message: `Invalid Fortnox API response structure. No recognizable file ID found. Response: ${JSON.stringify(uploadResponse.data, null, 2)}`,
          stepId: step.id
        });
        throw new Error(`Invalid Fortnox API response structure. No recognizable file ID found. Response: ${JSON.stringify(uploadResponse.data)}`);
      }
    }

    if (!fileId) {
      onLog({
        level: 'error',
        message: `Missing file ID in Fortnox Archive response: ${JSON.stringify(uploadResponse.data, null, 2)}`,
        stepId: step.id
      });
      throw new Error(`Missing file ID in Fortnox Archive response: ${JSON.stringify(uploadResponse.data)}`);
    }

    // Store file information in variables
    const variableName = step.variableName || getDefaultVariableName('fortnoxUploadFile', stepIndex);
    const fileResult = {
      fileId: fileId,
      filename: fileName,
      size: fileSize,
      path: filePath,
      fullResponse: archiveData
    };

    variables[variableName] = fileResult;

    onLog({
      level: 'info',
      message: `File uploaded successfully: ${fileName} (ID: ${fileId})`,
      stepId: step.id
    });

    return {
      success: true,
      variables: {
        [variableName]: fileResult
      }
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    onLog({
      level: 'error',
      message: `Error uploading file to Fortnox: ${errorMessage}`,
      stepId: step.id
    });

    return {
      success: false,
      error: errorMessage
    };
  }
}
